CLANG_CXX_LANGUAGE_STANDARD = c++20
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
EMBEDDED_CONTENT_CONTAINS_SWIFT = YES
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 $(inherited) SD_WEBP=1 $(inherited) PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/BVLinearGradient" "${PODS_ROOT}/Headers/Public/DoubleConversion" "${PODS_ROOT}/Headers/Public/FBLazyVector" "${PODS_ROOT}/Headers/Public/Firebase" "${PODS_ROOT}/Headers/Public/FirebaseCore" "${PODS_ROOT}/Headers/Public/FirebaseCoreExtension" "${PODS_ROOT}/Headers/Public/FirebaseInstallations" "${PODS_ROOT}/Headers/Public/FirebaseMessaging" "${PODS_ROOT}/Headers/Public/GoogleDataTransport" "${PODS_ROOT}/Headers/Public/GoogleUtilities" "${PODS_ROOT}/Headers/Public/PromisesObjC" "${PODS_ROOT}/Headers/Public/RCT-Folly" "${PODS_ROOT}/Headers/Public/RCTDeprecation" "${PODS_ROOT}/Headers/Public/RCTRequired" "${PODS_ROOT}/Headers/Public/RCTTypeSafety" "${PODS_ROOT}/Headers/Public/RNCAsyncStorage" "${PODS_ROOT}/Headers/Public/RNCClipboard" "${PODS_ROOT}/Headers/Public/RNCMaskedView" "${PODS_ROOT}/Headers/Public/RNCPicker" "${PODS_ROOT}/Headers/Public/RNDateTimePicker" "${PODS_ROOT}/Headers/Public/RNFBApp" "${PODS_ROOT}/Headers/Public/RNFBMessaging" "${PODS_ROOT}/Headers/Public/RNFS" "${PODS_ROOT}/Headers/Public/RNFastImage" "${PODS_ROOT}/Headers/Public/RNFileViewer" "${PODS_ROOT}/Headers/Public/RNGestureHandler" "${PODS_ROOT}/Headers/Public/RNPermissions" "${PODS_ROOT}/Headers/Public/RNReanimated" "${PODS_ROOT}/Headers/Public/RNSVG" "${PODS_ROOT}/Headers/Public/RNScreens" "${PODS_ROOT}/Headers/Public/RNShare" "${PODS_ROOT}/Headers/Public/RNSound" "${PODS_ROOT}/Headers/Public/RNVectorIcons" "${PODS_ROOT}/Headers/Public/React-Core" "${PODS_ROOT}/Headers/Public/React-Fabric" "${PODS_ROOT}/Headers/Public/React-FabricComponents" "${PODS_ROOT}/Headers/Public/React-FabricImage" "${PODS_ROOT}/Headers/Public/React-ImageManager" "${PODS_ROOT}/Headers/Public/React-Mapbuffer" "${PODS_ROOT}/Headers/Public/React-NativeModulesApple" "${PODS_ROOT}/Headers/Public/React-RCTAnimation" "${PODS_ROOT}/Headers/Public/React-RCTAppDelegate" "${PODS_ROOT}/Headers/Public/React-RCTBlob" "${PODS_ROOT}/Headers/Public/React-RCTFabric" "${PODS_ROOT}/Headers/Public/React-RCTText" "${PODS_ROOT}/Headers/Public/React-RuntimeApple" "${PODS_ROOT}/Headers/Public/React-RuntimeCore" "${PODS_ROOT}/Headers/Public/React-callinvoker" "${PODS_ROOT}/Headers/Public/React-cxxreact" "${PODS_ROOT}/Headers/Public/React-debug" "${PODS_ROOT}/Headers/Public/React-defaultsnativemodule" "${PODS_ROOT}/Headers/Public/React-domnativemodule" "${PODS_ROOT}/Headers/Public/React-featureflags" "${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule" "${PODS_ROOT}/Headers/Public/React-graphics" "${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule" "${PODS_ROOT}/Headers/Public/React-jsc" "${PODS_ROOT}/Headers/Public/React-jserrorhandler" "${PODS_ROOT}/Headers/Public/React-jsi" "${PODS_ROOT}/Headers/Public/React-jsiexecutor" "${PODS_ROOT}/Headers/Public/React-jsinspector" "${PODS_ROOT}/Headers/Public/React-logger" "${PODS_ROOT}/Headers/Public/React-microtasksnativemodule" "${PODS_ROOT}/Headers/Public/React-nativeconfig" "${PODS_ROOT}/Headers/Public/React-perflogger" "${PODS_ROOT}/Headers/Public/React-performancetimeline" "${PODS_ROOT}/Headers/Public/React-rendererconsistency" "${PODS_ROOT}/Headers/Public/React-rendererdebug" "${PODS_ROOT}/Headers/Public/React-runtimeexecutor" "${PODS_ROOT}/Headers/Public/React-runtimescheduler" "${PODS_ROOT}/Headers/Public/React-timing" "${PODS_ROOT}/Headers/Public/React-utils" "${PODS_ROOT}/Headers/Public/ReactCodegen" "${PODS_ROOT}/Headers/Public/ReactCommon" "${PODS_ROOT}/Headers/Public/SDWebImage" "${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder" "${PODS_ROOT}/Headers/Public/SocketRocket" "${PODS_ROOT}/Headers/Public/Yoga" "${PODS_ROOT}/Headers/Public/boost" "${PODS_ROOT}/Headers/Public/fmt" "${PODS_ROOT}/Headers/Public/glog" "${PODS_ROOT}/Headers/Public/libwebp" "${PODS_ROOT}/Headers/Public/nanopb" "${PODS_ROOT}/Headers/Public/react-native-blob-util" "${PODS_ROOT}/Headers/Public/react-native-camera" "${PODS_ROOT}/Headers/Public/react-native-document-picker" "${PODS_ROOT}/Headers/Public/react-native-geolocation" "${PODS_ROOT}/Headers/Public/react-native-html-to-pdf" "${PODS_ROOT}/Headers/Public/react-native-pager-view" "${PODS_ROOT}/Headers/Public/react-native-safe-area-context" "${PODS_ROOT}/Headers/Public/react-native-slider" "${PODS_ROOT}/Headers/Public/react-native-splash-screen" "$(PODS_ROOT)/DoubleConversion" $(inherited) ${PODS_ROOT}/Firebase/CoreOnly/Sources "$(PODS_ROOT)/boost" "$(PODS_ROOT)/boost" "$(PODS_ROOT)/boost-for-react-native" "$(PODS_ROOT)/glog" "$(PODS_ROOT)/RCT-Folly" "$(PODS_ROOT)/Headers/Public/React-hermes" "$(PODS_ROOT)/Headers/Public/hermes-engine" "$(PODS_ROOT)/../../node_modules/react-native/ReactCommon" "$(PODS_ROOT)/../../node_modules/react-native-reanimated/apple" "$(PODS_ROOT)/../../node_modules/react-native-reanimated/Common/cpp" "$(PODS_ROOT)/Headers/Private/React-Core" "$(PODS_ROOT)/Headers/Private/React-Core" "$(PODS_ROOT)/Headers/Private/Yoga"
LIBRARY_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/BVLinearGradient" "${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly" "${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation" "${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety" "${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage" "${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard" "${PODS_CONFIGURATION_BUILD_DIR}/RNCMaskedView" "${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker" "${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp" "${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging" "${PODS_CONFIGURATION_BUILD_DIR}/RNFS" "${PODS_CONFIGURATION_BUILD_DIR}/RNFastImage" "${PODS_CONFIGURATION_BUILD_DIR}/RNFileViewer" "${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler" "${PODS_CONFIGURATION_BUILD_DIR}/RNPermissions" "${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated" "${PODS_CONFIGURATION_BUILD_DIR}/RNSVG" "${PODS_CONFIGURATION_BUILD_DIR}/RNScreens" "${PODS_CONFIGURATION_BUILD_DIR}/RNShare" "${PODS_CONFIGURATION_BUILD_DIR}/RNSound" "${PODS_CONFIGURATION_BUILD_DIR}/RNVectorIcons" "${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen" "${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager" "${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer" "${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore" "${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact" "${PODS_CONFIGURATION_BUILD_DIR}/React-debug" "${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics" "${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsc" "${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsi" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector" "${PODS_CONFIGURATION_BUILD_DIR}/React-logger" "${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig" "${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger" "${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug" "${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler" "${PODS_CONFIGURATION_BUILD_DIR}/React-utils" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" "${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket" "${PODS_CONFIGURATION_BUILD_DIR}/Yoga" "${PODS_CONFIGURATION_BUILD_DIR}/fmt" "${PODS_CONFIGURATION_BUILD_DIR}/glog" "${PODS_CONFIGURATION_BUILD_DIR}/libwebp" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-blob-util" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-camera" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-geolocation" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-html-to-pdf" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-pager-view" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-slider" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-splash-screen" "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen/React_Codegen.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Private/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/BVLinearGradient/BVLinearGradient.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/CoreModules/React-CoreModules.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/FBLPromises/PromisesObjC.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/FirebaseCore/FirebaseCore.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/FirebaseCoreExtension/FirebaseCoreExtension.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/FirebaseMessaging/FirebaseMessaging.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTAnimation/React-RCTAnimation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTBlob/React-RCTBlob.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTImage/React-RCTImage.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTLinking/React-RCTLinking.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTNetwork/React-RCTNetwork.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTSettings/React-RCTSettings.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTText/React-RCTText.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTVibration/React-RCTVibration.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNCAsyncStorage/RNCAsyncStorage.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNCClipboard/RNCClipboard.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNCMaskedView/RNCMaskedView.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNCPicker/RNCPicker.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNDateTimePicker/RNDateTimePicker.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFBApp/RNFBApp.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFBMessaging/RNFBMessaging.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFS/RNFS.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFastImage/RNFastImage.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFileViewer/RNFileViewer.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNGestureHandler/RNGestureHandler.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNPermissions/RNPermissions.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNSVG/RNSVG.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNScreens/RNScreens.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNShare/RNShare.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNSound/RNSound.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNVectorIcons/RNVectorIcons.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React/React-Core.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/React-RuntimeApple.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_jsc/React-jsc.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/SDWebImage/SDWebImage.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/SocketRocket/SocketRocket.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/cxxreact/React-cxxreact.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/fmt/fmt.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jserrorhandler/React-jserrorhandler.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsireact/React-jsiexecutor.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/libwebp/libwebp.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/logger/React-logger.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/nanopb/nanopb.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_config/React-nativeconfig.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_blob_util/react-native-blob-util.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_camera/react-native-camera.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_document_picker/react-native-document-picker.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_geolocation/react-native-geolocation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_html_to_pdf/react-native-html-to-pdf.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_pager_view/react-native-pager-view.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_safe_area_context/react-native-safe-area-context.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_slider/react-native-slider.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_splash_screen/react-native-splash-screen.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_performance_timeline/React-performancetimeline.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_components_image/React-FabricImage.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_consistency/React-rendererconsistency.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_mapbuffer/React-Mapbuffer.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_runtimescheduler/React-runtimescheduler.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_runtime/React-RuntimeCore.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/reactperflogger/React-perflogger.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap" $(inherited)  $(inherited) -DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED  -DREACT_NATIVE_MINOR_VERSION=76 -DREANIMATED_VERSION=3.17.5   $(inherited) -DREACT_NATIVE_MINOR_VERSION=76 -DNDEBUG
OTHER_CPLUSPLUSFLAGS = $(inherited) -DNDEBUG -DRCT_NEW_ARCH_ENABLED=1 -DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32
OTHER_LDFLAGS = $(inherited) -ObjC -l"BVLinearGradient" -l"DoubleConversion" -l"FirebaseCore" -l"FirebaseCoreExtension" -l"FirebaseCoreInternal" -l"FirebaseInstallations" -l"FirebaseMessaging" -l"GoogleDataTransport" -l"GoogleUtilities" -l"PromisesObjC" -l"RCT-Folly" -l"RCTDeprecation" -l"RCTTypeSafety" -l"RNCAsyncStorage" -l"RNCClipboard" -l"RNCMaskedView" -l"RNCPicker" -l"RNDateTimePicker" -l"RNFBApp" -l"RNFBMessaging" -l"RNFS" -l"RNFastImage" -l"RNFileViewer" -l"RNGestureHandler" -l"RNPermissions" -l"RNReanimated" -l"RNSVG" -l"RNScreens" -l"RNShare" -l"RNSound" -l"RNVectorIcons" -l"React-Codegen" -l"React-Core" -l"React-CoreModules" -l"React-Fabric" -l"React-FabricComponents" -l"React-FabricImage" -l"React-ImageManager" -l"React-Mapbuffer" -l"React-NativeModulesApple" -l"React-RCTAnimation" -l"React-RCTAppDelegate" -l"React-RCTBlob" -l"React-RCTFabric" -l"React-RCTImage" -l"React-RCTLinking" -l"React-RCTNetwork" -l"React-RCTSettings" -l"React-RCTText" -l"React-RCTVibration" -l"React-RuntimeApple" -l"React-RuntimeCore" -l"React-cxxreact" -l"React-debug" -l"React-defaultsnativemodule" -l"React-domnativemodule" -l"React-featureflags" -l"React-featureflagsnativemodule" -l"React-graphics" -l"React-idlecallbacksnativemodule" -l"React-jsc" -l"React-jserrorhandler" -l"React-jsi" -l"React-jsiexecutor" -l"React-jsinspector" -l"React-logger" -l"React-microtasksnativemodule" -l"React-nativeconfig" -l"React-perflogger" -l"React-performancetimeline" -l"React-rendererconsistency" -l"React-rendererdebug" -l"React-runtimescheduler" -l"React-utils" -l"ReactCodegen" -l"ReactCommon" -l"SDWebImage" -l"SDWebImageWebPCoder" -l"SocketRocket" -l"Yoga" -l"c++" -l"c++abi" -l"fmt" -l"glog" -l"icucore" -l"libwebp" -l"nanopb" -l"react-native-blob-util" -l"react-native-camera" -l"react-native-document-picker" -l"react-native-geolocation" -l"react-native-html-to-pdf" -l"react-native-pager-view" -l"react-native-safe-area-context" -l"react-native-slider" -l"react-native-splash-screen" -l"sqlite3" -l"z" -framework "Accelerate" -framework "AssetsLibrary" -framework "AudioToolbox" -framework "CFNetwork" -framework "CoreGraphics" -framework "CoreLocation" -framework "CoreTelephony" -framework "Foundation" -framework "ImageIO" -framework "JavaScriptCore" -framework "MobileCoreServices" -framework "QuartzCore" -framework "Security" -framework "SystemConfiguration" -framework "UIKit" -weak_framework "LinkPresentation" -weak_framework "UserNotifications"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen/React_Codegen.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Private/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/BVLinearGradient/BVLinearGradient.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/CoreModules/React-CoreModules.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/FBLPromises/PromisesObjC.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/FirebaseCore/FirebaseCore.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/FirebaseCoreExtension/FirebaseCoreExtension.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/FirebaseInstallations/FirebaseInstallations.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/FirebaseMessaging/FirebaseMessaging.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/GoogleDataTransport/GoogleDataTransport.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/GoogleUtilities/GoogleUtilities.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTAnimation/React-RCTAnimation.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTBlob/React-RCTBlob.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTImage/React-RCTImage.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTLinking/React-RCTLinking.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTNetwork/React-RCTNetwork.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTSettings/React-RCTSettings.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTText/React-RCTText.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTVibration/React-RCTVibration.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNCAsyncStorage/RNCAsyncStorage.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNCClipboard/RNCClipboard.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNCMaskedView/RNCMaskedView.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNCPicker/RNCPicker.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNDateTimePicker/RNDateTimePicker.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFBApp/RNFBApp.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFBMessaging/RNFBMessaging.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFS/RNFS.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFastImage/RNFastImage.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNFileViewer/RNFileViewer.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNGestureHandler/RNGestureHandler.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNPermissions/RNPermissions.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNSVG/RNSVG.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNScreens/RNScreens.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNShare/RNShare.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNSound/RNSound.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNVectorIcons/RNVectorIcons.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React/React-Core.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/React-RuntimeApple.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_jsc/React-jsc.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/SDWebImage/SDWebImage.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/SocketRocket/SocketRocket.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/cxxreact/React-cxxreact.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/fmt/fmt.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jserrorhandler/React-jserrorhandler.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsireact/React-jsiexecutor.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/libwebp/libwebp.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/logger/React-logger.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/nanopb/nanopb.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_config/React-nativeconfig.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_blob_util/react-native-blob-util.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_camera/react-native-camera.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_document_picker/react-native-document-picker.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_geolocation/react-native-geolocation.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_html_to_pdf/react-native-html-to-pdf.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_pager_view/react-native-pager-view.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_safe_area_context/react-native-safe-area-context.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_slider/react-native-slider.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_native_splash_screen/react-native-splash-screen.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_performance_timeline/React-performancetimeline.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_components_image/React-FabricImage.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_consistency/React-rendererconsistency.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_mapbuffer/React-Mapbuffer.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_runtimescheduler/React-runtimescheduler.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_runtime/React-RuntimeCore.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/reactperflogger/React-perflogger.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
SWIFT_INCLUDE_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/Firebase" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen"
USER_HEADER_SEARCH_PATHS = $(inherited) $(SRCROOT)/libwebp/src
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES

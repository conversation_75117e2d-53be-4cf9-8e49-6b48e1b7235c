<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>BVLinearGradient.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>DoubleConversion.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FBLazyVector.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Firebase.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCore-FirebaseCore_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreExtension-FirebaseCoreExtension_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreExtension.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreInternal-FirebaseCoreInternal_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreInternal.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseInstallations-FirebaseInstallations_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseInstallations.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseMessaging-FirebaseMessaging_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseMessaging.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport-GoogleDataTransport_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities-GoogleUtilities_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-PSquare-PSquareTests.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-PSquare.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC-FBLPromises_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCT-Folly-RCT-Folly_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCT-Folly.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCTDeprecation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCTRequired.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RCTTypeSafety.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNCAsyncStorage-RNCAsyncStorage_resources.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNCAsyncStorage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNCClipboard.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNCMaskedView.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNCPicker.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNDateTimePicker.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNFBApp.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNFBMessaging.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNFS.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNFastImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNFileViewer.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNGestureHandler.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNPermissions.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNReanimated.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNSVG-RNSVGFilters.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNSVG.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNScreens.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNShare.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNSound.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>RNVectorIcons.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-Codegen.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-Core-React-Core_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-Core.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-CoreModules.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-Fabric.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-FabricComponents.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-FabricImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-ImageManager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-Mapbuffer.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-NativeModulesApple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTActionSheet.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTAnimation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTAppDelegate.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTBlob.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTFabric.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTLinking.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTNetwork.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTSettings.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTText.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RCTVibration.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RuntimeApple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-RuntimeCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-callinvoker.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-cxxreact-React-cxxreact_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-cxxreact.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-debug.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-defaultsnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-domnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-featureflags.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-featureflagsnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-graphics.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-idlecallbacksnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jsc.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jserrorhandler.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jsi.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jsiexecutor.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jsinspector.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-jsitracing.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-logger.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-microtasksnativemodule.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-nativeconfig.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-perflogger.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-performancetimeline.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-rendererconsistency.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-rendererdebug.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-rncore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-runtimeexecutor.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-runtimescheduler.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-timing.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React-utils.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>React.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>ReactCodegen.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>ReactCommon.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SDWebImageWebPCoder.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SocketRocket.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Yoga.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>boost-boost_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>boost.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>fmt.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>glog-glog_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>glog.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>libwebp.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>nanopb-nanopb_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>nanopb.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-blob-util-ReactNativeBlobUtilPrivacyInfo.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-blob-util.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-camera.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-document-picker.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-geolocation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-html-to-pdf.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-pager-view.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-safe-area-context.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-slider.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>react-native-splash-screen.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
